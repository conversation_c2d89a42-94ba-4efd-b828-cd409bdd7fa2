@echo off
setlocal enabledelayedexpansion

REM End-to-End Test Runner Script for Windows
REM This script helps setup and run the complete E2E test

echo.
echo 🚀 ATMA Backend - End-to-End Test Runner
echo ==================================================

REM Set default environment variables if not set
if not defined DB_HOST set DB_HOST=localhost
if not defined DB_PORT set DB_PORT=5432
if not defined DB_NAME set DB_NAME=atma_db
if not defined DB_USER set DB_USER=postgres
if not defined DB_PASSWORD set DB_PASSWORD=password

REM Parse command line arguments
set SKIP_CHECKS=false
set SKIP_WAIT=false

:parse_args
if "%1"=="--skip-checks" (
    set SKIP_CHECKS=true
    shift
    goto parse_args
)
if "%1"=="--skip-wait" (
    set SKIP_WAIT=true
    shift
    goto parse_args
)
if "%1"=="--help" (
    goto show_help
)
if "%1"=="/?" (
    goto show_help
)
if not "%1"=="" (
    echo ❌ Unknown option: %1
    goto show_help
)

REM Check prerequisites
if "%SKIP_CHECKS%"=="false" (
    echo.
    echo 🔍 Checking prerequisites...
    
    REM Check Node.js
    node --version >nul 2>&1
    if errorlevel 1 (
        echo   Node.js: ❌ Not found
        echo   Please install Node.js from https://nodejs.org/
        exit /b 1
    ) else (
        for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
        echo   Node.js: ✅ (!NODE_VERSION!)
    )
    
    REM Check npm
    npm --version >nul 2>&1
    if errorlevel 1 (
        echo   npm: ❌ Not found
        exit /b 1
    ) else (
        for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
        echo   npm: ✅ (v!NPM_VERSION!)
    )
    
    REM Check if dependencies are installed
    if exist "node_modules" (
        echo   Dependencies: ✅
    ) else (
        echo   Dependencies: ⚠️  Installing...
        npm install
        if errorlevel 1 (
            echo   ❌ Failed to install dependencies
            exit /b 1
        )
    )
    
    echo.
    echo 🔌 Checking required ports...
    
    REM Check if services are running by trying to connect
    call :check_service "API Gateway" 3000 "/"
    call :check_service "Auth Service" 3001 "/health"
    call :check_service "Archive Service" 3002 "/health"
    call :check_service "Assessment Service" 3003 "/health"
    call :check_service "Analysis Worker" 3004 "/health"
    call :check_service "Notification Service" 3005 "/"
    
    REM Check PostgreSQL
    netstat -an | findstr ":5432" >nul 2>&1
    if errorlevel 1 (
        echo   Port 5432 (PostgreSQL): ❌
        set SERVICES_OK=false
    ) else (
        echo   Port 5432 (PostgreSQL): ✅
    )
    
    REM Check RabbitMQ
    netstat -an | findstr ":5672" >nul 2>&1
    if errorlevel 1 (
        echo   Port 5672 (RabbitMQ): ❌
        set SERVICES_OK=false
    ) else (
        echo   Port 5672 (RabbitMQ): ✅
    )
    
    if "!SERVICES_OK!"=="false" (
        echo.
        echo ❌ Some required services are not running!
        echo 💡 Please start all services first:
        echo    npm run dev:all
        echo    # Or start services individually
        exit /b 1
    )
) else (
    echo ⚠️  Skipping prerequisite checks
)

REM Wait for services
if "%SKIP_WAIT%"=="false" (
    echo.
    echo ⏳ Waiting for services to be ready...
    
    set /a attempt=1
    set /a max_attempts=12
    
    :wait_loop
    echo Attempt !attempt!/!max_attempts!:
    
    set all_healthy=true
    
    REM Check each service health
    call :check_service_health "API Gateway" 3000 "/"
    call :check_service_health "Auth Service" 3001 "/health"
    call :check_service_health "Archive Service" 3002 "/health"
    call :check_service_health "Assessment Service" 3003 "/health"
    call :check_service_health "Analysis Worker" 3004 "/health"
    call :check_service_health "Notification Service" 3005 "/"
    
    if "!all_healthy!"=="true" (
        echo ✅ All services are healthy!
        goto run_test
    )
    
    if !attempt! lss !max_attempts! (
        echo ⏳ Waiting 5 seconds before next check...
        timeout /t 5 /nobreak >nul
        set /a attempt+=1
        goto wait_loop
    )
    
    echo ❌ Some services are not responding after !max_attempts! attempts
    exit /b 1
) else (
    echo ⚠️  Skipping service health checks
)

:run_test
echo.
echo 🧪 Running End-to-End Test...
echo ==================================================

REM Run the test
node scripts/test-end-to-end.js
if errorlevel 1 (
    echo.
    echo ❌ End-to-End Test FAILED!
    exit /b 1
) else (
    echo.
    echo 🎉 End-to-End Test PASSED!
    echo ✅ All tests completed successfully!
    exit /b 0
)

REM Function to check service
:check_service
set service_name=%~1
set port=%~2
set endpoint=%~3

echo   Checking %service_name% (port %port%)...

REM Try to connect to the service
curl -s "http://localhost:%port%%endpoint%" >nul 2>&1
if errorlevel 1 (
    echo     ❌ Not responding
    set SERVICES_OK=false
) else (
    echo     ✅ OK
)
goto :eof

REM Function to check service health
:check_service_health
set service_name=%~1
set port=%~2
set endpoint=%~3

echo   Checking %service_name% (port %port%)...

curl -s "http://localhost:%port%%endpoint%" >nul 2>&1
if errorlevel 1 (
    echo     ❌
    set all_healthy=false
) else (
    echo     ✅
)
goto :eof

:show_help
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   --skip-checks    Skip prerequisite checks
echo   --skip-wait      Skip waiting for services
echo   --help           Show this help message
echo.
echo Environment Variables:
echo   DB_HOST          Database host (default: localhost)
echo   DB_PORT          Database port (default: 5432)
echo   DB_NAME          Database name (default: atma_db)
echo   DB_USER          Database user (default: postgres)
echo   DB_PASSWORD      Database password (default: password)
echo.
echo Examples:
echo   %0                           # Run full test with all checks
echo   %0 --skip-checks             # Skip prerequisite checks
echo   %0 --skip-wait               # Skip service health checks
echo   set DB_PASSWORD=mypass ^& %0  # Use custom database password
exit /b 0
