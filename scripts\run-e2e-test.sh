#!/bin/bash

# End-to-End Test Runner Script
# This script helps setup and run the complete E2E test

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICES=("api-gateway" "auth-service" "archive-service" "assessment-service" "analysis-worker" "notification-service")
REQUIRED_PORTS=(3000 3001 3002 3003 3004 3005 5432 5672)
WAIT_TIME=30

echo -e "${BLUE}🚀 ATMA Backend - End-to-End Test Runner${NC}"
echo "=================================================="

# Function to check if a port is in use
check_port() {
    local port=$1
    if command -v netstat >/dev/null 2>&1; then
        netstat -tuln | grep ":$port " >/dev/null 2>&1
    elif command -v ss >/dev/null 2>&1; then
        ss -tuln | grep ":$port " >/dev/null 2>&1
    elif command -v lsof >/dev/null 2>&1; then
        lsof -i :$port >/dev/null 2>&1
    else
        echo -e "${YELLOW}⚠️  Cannot check port $port - no suitable command found${NC}"
        return 0
    fi
}

# Function to check service health
check_service_health() {
    local service_name=$1
    local port=$2
    local endpoint=${3:-"/health"}
    
    echo -n "  Checking $service_name (port $port)... "
    
    if curl -s "http://localhost:$port$endpoint" >/dev/null 2>&1; then
        echo -e "${GREEN}✅${NC}"
        return 0
    else
        echo -e "${RED}❌${NC}"
        return 1
    fi
}

# Function to wait for services
wait_for_services() {
    echo -e "\n${YELLOW}⏳ Waiting for services to be ready...${NC}"
    
    local max_attempts=12
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        echo "Attempt $attempt/$max_attempts:"
        
        local all_healthy=true
        
        # Check each service
        check_service_health "API Gateway" 3000 "/" || all_healthy=false
        check_service_health "Auth Service" 3001 "/health" || all_healthy=false
        check_service_health "Archive Service" 3002 "/health" || all_healthy=false
        check_service_health "Assessment Service" 3003 "/health" || all_healthy=false
        check_service_health "Analysis Worker" 3004 "/health" || all_healthy=false
        check_service_health "Notification Service" 3005 "/" || all_healthy=false
        
        if [ "$all_healthy" = true ]; then
            echo -e "${GREEN}✅ All services are healthy!${NC}"
            return 0
        fi
        
        if [ $attempt -lt $max_attempts ]; then
            echo -e "${YELLOW}⏳ Waiting 5 seconds before next check...${NC}"
            sleep 5
        fi
        
        ((attempt++))
    done
    
    echo -e "${RED}❌ Some services are not responding after $max_attempts attempts${NC}"
    return 1
}

# Function to check prerequisites
check_prerequisites() {
    echo -e "\n${BLUE}🔍 Checking prerequisites...${NC}"
    
    # Check Node.js
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version)
        echo -e "  Node.js: ${GREEN}✅${NC} ($node_version)"
    else
        echo -e "  Node.js: ${RED}❌ Not found${NC}"
        exit 1
    fi
    
    # Check npm
    if command -v npm >/dev/null 2>&1; then
        local npm_version=$(npm --version)
        echo -e "  npm: ${GREEN}✅${NC} (v$npm_version)"
    else
        echo -e "  npm: ${RED}❌ Not found${NC}"
        exit 1
    fi
    
    # Check if dependencies are installed
    if [ -d "node_modules" ]; then
        echo -e "  Dependencies: ${GREEN}✅${NC}"
    else
        echo -e "  Dependencies: ${YELLOW}⚠️  Installing...${NC}"
        npm install
    fi
    
    # Check required ports
    echo -e "\n${BLUE}🔌 Checking required ports...${NC}"
    local ports_ok=true
    
    for port in "${REQUIRED_PORTS[@]}"; do
        if check_port $port; then
            case $port in
                3000) echo -e "  Port $port (API Gateway): ${GREEN}✅${NC}" ;;
                3001) echo -e "  Port $port (Auth Service): ${GREEN}✅${NC}" ;;
                3002) echo -e "  Port $port (Archive Service): ${GREEN}✅${NC}" ;;
                3003) echo -e "  Port $port (Assessment Service): ${GREEN}✅${NC}" ;;
                3004) echo -e "  Port $port (Analysis Worker): ${GREEN}✅${NC}" ;;
                3005) echo -e "  Port $port (Notification Service): ${GREEN}✅${NC}" ;;
                5432) echo -e "  Port $port (PostgreSQL): ${GREEN}✅${NC}" ;;
                5672) echo -e "  Port $port (RabbitMQ): ${GREEN}✅${NC}" ;;
                *) echo -e "  Port $port: ${GREEN}✅${NC}" ;;
            esac
        else
            case $port in
                3000) echo -e "  Port $port (API Gateway): ${RED}❌${NC}" ;;
                3001) echo -e "  Port $port (Auth Service): ${RED}❌${NC}" ;;
                3002) echo -e "  Port $port (Archive Service): ${RED}❌${NC}" ;;
                3003) echo -e "  Port $port (Assessment Service): ${RED}❌${NC}" ;;
                3004) echo -e "  Port $port (Analysis Worker): ${RED}❌${NC}" ;;
                3005) echo -e "  Port $port (Notification Service): ${RED}❌${NC}" ;;
                5432) echo -e "  Port $port (PostgreSQL): ${RED}❌${NC}" ;;
                5672) echo -e "  Port $port (RabbitMQ): ${RED}❌${NC}" ;;
                *) echo -e "  Port $port: ${RED}❌${NC}" ;;
            esac
            ports_ok=false
        fi
    done
    
    if [ "$ports_ok" = false ]; then
        echo -e "\n${RED}❌ Some required services are not running!${NC}"
        echo -e "${YELLOW}💡 Please start all services first:${NC}"
        echo "   npm run dev:all"
        echo "   # Or start services individually"
        exit 1
    fi
}

# Function to run the test
run_test() {
    echo -e "\n${BLUE}🧪 Running End-to-End Test...${NC}"
    echo "=================================================="
    
    # Set environment variables if not set
    export DB_HOST=${DB_HOST:-localhost}
    export DB_PORT=${DB_PORT:-5432}
    export DB_NAME=${DB_NAME:-atma_db}
    export DB_USER=${DB_USER:-postgres}
    export DB_PASSWORD=${DB_PASSWORD:-password}
    
    # Run the test
    if node scripts/test-end-to-end.js; then
        echo -e "\n${GREEN}🎉 End-to-End Test PASSED!${NC}"
        return 0
    else
        echo -e "\n${RED}❌ End-to-End Test FAILED!${NC}"
        return 1
    fi
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --skip-checks    Skip prerequisite checks"
    echo "  --skip-wait      Skip waiting for services"
    echo "  --help           Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  DB_HOST          Database host (default: localhost)"
    echo "  DB_PORT          Database port (default: 5432)"
    echo "  DB_NAME          Database name (default: atma_db)"
    echo "  DB_USER          Database user (default: postgres)"
    echo "  DB_PASSWORD      Database password (default: password)"
    echo ""
    echo "Examples:"
    echo "  $0                           # Run full test with all checks"
    echo "  $0 --skip-checks             # Skip prerequisite checks"
    echo "  $0 --skip-wait               # Skip service health checks"
    echo "  DB_PASSWORD=mypass $0        # Use custom database password"
}

# Parse command line arguments
SKIP_CHECKS=false
SKIP_WAIT=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-checks)
            SKIP_CHECKS=true
            shift
            ;;
        --skip-wait)
            SKIP_WAIT=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Unknown option: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    # Check prerequisites
    if [ "$SKIP_CHECKS" = false ]; then
        check_prerequisites
    else
        echo -e "${YELLOW}⚠️  Skipping prerequisite checks${NC}"
    fi
    
    # Wait for services
    if [ "$SKIP_WAIT" = false ]; then
        wait_for_services
    else
        echo -e "${YELLOW}⚠️  Skipping service health checks${NC}"
    fi
    
    # Run the test
    if run_test; then
        echo -e "\n${GREEN}✅ All tests completed successfully!${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ Tests failed!${NC}"
        exit 1
    fi
}

# Handle Ctrl+C
trap 'echo -e "\n${YELLOW}🛑 Test interrupted by user${NC}"; exit 130' INT

# Run main function
main
