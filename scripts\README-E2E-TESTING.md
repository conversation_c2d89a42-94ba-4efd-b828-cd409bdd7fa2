# End-to-End Testing Guide

Panduan lengkap untuk menjalankan testing end-to-end pada ekosistem ATMA Backend.

## 📋 Overview

End-to-End test ini memverifikasi seluruh flow dari client request hingga data tersimpan di database:

```
Client → API Gateway → Assessment Service → RabbitMQ → Analysis Worker → Archive Service → Notification Service
   ↓                                                                                              ↓
   └─────────────── API Gateway ← Archive Service ←──────────────────────────────────────────────┘
                   (Mengambil hasil)
```

## 🎯 Test Coverage

Test ini mencakup:

- ✅ **User Authentication**: Register & login user test
- ✅ **JWT Verification**: API Gateway authentication
- ✅ **Assessment Submission**: Submit assessment data via API Gateway
- ✅ **Queue Processing**: RabbitMQ message handling
- ✅ **AI Analysis**: Analysis Worker processing
- ✅ **Data Storage**: Archive Service database operations
- ✅ **WebSocket Notifications**: Real-time notifications
- ✅ **Result Retrieval**: Fetch results via API
- ✅ **Database Verification**: Direct database validation

## 🔧 Prerequisites

### 1. Services Running

Pastikan semua services berjalan:

```bash
# Start all services
npm run dev:all

# Or start individually
npm run dev:api-gateway
npm run dev:auth
npm run dev:archive
npm run dev:assessment
npm run dev:analysis
npm run dev:notification
```

### 2. Database & Message Queue

- **PostgreSQL**: Running on port 5432
- **RabbitMQ**: Running on port 5672

### 3. Dependencies

Install testing dependencies:

```bash
# Install E2E test dependencies
npm run test:e2e:install

# Or manually
npm install axios pg socket.io-client
```

## 🚀 Running Tests

### Method 1: Using Helper Scripts

**Linux/macOS:**
```bash
# Make script executable
chmod +x scripts/run-e2e-test.sh

# Run full test
./scripts/run-e2e-test.sh

# Skip prerequisite checks
./scripts/run-e2e-test.sh --skip-checks

# Skip service health checks
./scripts/run-e2e-test.sh --skip-wait
```

**Windows:**
```cmd
# Run full test
scripts\run-e2e-test.bat

# Skip prerequisite checks
scripts\run-e2e-test.bat --skip-checks

# Skip service health checks
scripts\run-e2e-test.bat --skip-wait
```

### Method 2: Direct Node.js

```bash
# Run test directly
npm run test:e2e

# Or
node scripts/test-end-to-end.js
```

### Method 3: Custom Environment

```bash
# Custom database configuration
DB_HOST=localhost \
DB_PORT=5432 \
DB_NAME=atma_db \
DB_USER=postgres \
DB_PASSWORD=mypassword \
node scripts/test-end-to-end.js
```

## 📊 Test Flow Details

### 1. Setup Phase
- Connect to database
- Create test user (`<EMAIL>`)
- Login and get JWT token
- Setup WebSocket connection

### 2. Assessment Submission
- Submit complete assessment data:
  - RIASEC scores
  - OCEAN personality traits
  - VIA-IS character strengths
  - Multiple Intelligences
  - Cognitive Style Index

### 3. Processing Phase
- Monitor job status via API
- Wait for WebSocket notification
- Track analysis completion

### 4. Verification Phase
- Fetch results via API Gateway
- Verify data in database directly
- Validate all assessment components
- Check persona profile generation

### 5. Cleanup Phase
- Close WebSocket connection
- Close database connection
- Optional: Clean test data

## 📝 Test Data

Test menggunakan data assessment lengkap:

```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    // ... 24 character strengths total
  },
  "multipleIntelligences": {
    "linguistic": 85,
    "logicalMathematical": 90,
    "spatial": 75,
    // ... 8 intelligence types total
  },
  "cognitiveStyleIndex": {
    "analytic": 80,
    "intuitive": 60
  }
}
```

## 🔍 Expected Results

### Successful Test Output

```
🚀 Starting End-to-End Test
============================================================

🔌 Connecting to database...
✅ Database connected

📝 Setting up test user...
✅ Test user logged in
   User ID: 550e8400-e29b-41d4-a716-446655440001
   Token: eyJhbGciOiJIUzI1NiIs...

🔌 Setting up WebSocket connection...
✅ WebSocket connected

📊 Submitting assessment data...
✅ Assessment submitted successfully
   Job ID: 550e8400-e29b-41d4-a716-446655440000
   Status: queued
   Estimated time: 2-5 minutes

⏳ Waiting for analysis completion...
   Status: processing (15s elapsed)
   Status: processing (20s elapsed)
🔔 Received analysis completion notification
✅ Analysis completed in 45 seconds

📥 Fetching analysis results...
✅ Results fetched successfully
   Result ID: 550e8400-e29b-41d4-a716-446655440002
   Status: completed
   Archetype: The Innovator
   Strengths: 5 items
   Challenges: 3 items
   Career Recommendations: 8 items

🔍 Verifying data in database...
✅ Data verified in database
   Record ID: 550e8400-e29b-41d4-a716-446655440002
   User ID: 550e8400-e29b-41d4-a716-446655440001
   Status: completed
   Assessment data keys: riasec, ocean, viaIs, multipleIntelligences, cognitiveStyleIndex
   ✅ All assessment data types present
   Persona archetype: The Innovator
   ✅ Persona profile data present

============================================================
🎉 End-to-End Test PASSED

Flow verified:
  ✅ Client → API Gateway → Assessment Service
  ✅ Assessment Service → RabbitMQ → Analysis Worker
  ✅ Analysis Worker → Archive Service (save)
  ✅ Analysis Worker → Notification Service
  ✅ Client ← WebSocket ← Notification Service
  ✅ Client → API Gateway → Archive Service (fetch)
  ✅ Database verification
```

## 🐛 Troubleshooting

### Common Issues

**1. Services Not Running**
```
❌ Some required services are not running!
💡 Please start all services first:
   npm run dev:all
```
**Solution**: Start all microservices

**2. Database Connection Failed**
```
❌ Database connection failed: ECONNREFUSED
```
**Solution**: 
- Check PostgreSQL is running
- Verify database credentials
- Check database exists

**3. Analysis Timeout**
```
❌ Analysis timeout - exceeded maximum wait time
```
**Solution**:
- Check Analysis Worker logs
- Verify Google AI API key
- Check RabbitMQ queue

**4. WebSocket Connection Failed**
```
❌ WebSocket connection failed
```
**Solution**:
- Check Notification Service is running
- Verify JWT token is valid
- Check CORS configuration

### Debug Mode

Enable detailed logging:

```bash
# Set debug environment
DEBUG=* node scripts/test-end-to-end.js

# Or check individual service logs
cd analysis-worker && npm run dev
cd notification-service && npm run dev
```

## 📈 Performance Expectations

- **Total Test Time**: 1-3 minutes
- **Analysis Processing**: 30-120 seconds
- **API Response Time**: < 1 second
- **WebSocket Notification**: < 5 seconds after completion

## 🔒 Security Notes

- Test user credentials are temporary
- JWT tokens expire after test
- Test data can be cleaned up automatically
- No production data is affected

## 📚 Related Documentation

- [Flow Logic Documentation](../FLOW-LOGIC.md)
- [API Documentation](../api-gateway/API-DOCS.md)
- [Database Schema](../scripts/init-databases.sql)
- [Service Integration Guides](../archive-service/INTEGRATION-GUIDE.md)
