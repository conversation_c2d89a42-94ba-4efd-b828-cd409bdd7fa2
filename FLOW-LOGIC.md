# ATMA Backend - Flow Logic Ecosystem

Dokumen ini menjelaskan alur kerja lengkap ekosistem ATMA Backend dari client request hingga notification dan pengambilan hasil analisis.

## 🔄 Overview Flow

```
Client → API Gateway → Assessment Service → RabbitMQ → Analysis Worker → Archive Service → Notification Service
   ↓                                                                                              ↓
   └─────────────── API Gateway ← Archive Service ←──────────────────────────────────────────────┘
                   (Mengambil hasil)
```

## 📋 Detailed Flow Steps

### 1. Client Request ke API Gateway

**Endpoint:** `POST /assessments/submit`

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70
  },
  "multipleIntelligences": {
    "linguistic": 85,
    "logicalMathematical": 90,
    "spatial": 75
  },
  "cognitiveStyleIndex": {
    "analytic": 80,
    "intuitive": 60
  }
}
```

### 2. API Gateway - JWT Verification & Routing

1. **JWT Verification**: API Gateway memverifikasi JWT token menggunakan middleware `authenticateToken`
2. **User Context**: Ekstrak informasi user (id, email, tokenBalance) dari JWT
3. **Request Enhancement**: Menambahkan `userId` dan `userEmail` ke request body
4. **Routing**: Forward request ke Assessment Service di port 3003

### 3. Assessment Service - Processing & Queue

1. **Validation**: Validasi data assessment (RIASEC, OCEAN, VIA-IS, Multiple Intelligences, Cognitive Style Index)
2. **Token Management**: Kurangi token balance user (biaya analisis)
3. **Job Creation**: 
   - Generate unique `jobId` menggunakan UUID
   - Create job tracker dengan status `QUEUED`
4. **RabbitMQ Publishing**: Publish job ke queue dengan format:

```json
{
  "jobId": "550e8400-e29b-41d4-a716-************",
  "userId": "550e8400-e29b-41d4-a716-************",
  "userEmail": "<EMAIL>",
  "assessmentData": {
    "riasec": { ... },
    "ocean": { ... },
    "viaIs": { ... },
    "multipleIntelligences": { ... },
    "cognitiveStyleIndex": { ... }
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "retryCount": 0
}
```

**Response ke Client:**
```json
{
  "success": true,
  "message": "Assessment submitted successfully and queued for analysis",
  "data": {
    "jobId": "550e8400-e29b-41d4-a716-************",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes"
  }
}
```

### 4. Analysis Worker - Processing

1. **Queue Consumption**: Consume message dari RabbitMQ queue `assessment_analysis`
2. **Data Validation**: Validasi struktur data assessment
3. **AI Processing**: 
   - Build prompt berdasarkan assessment data
   - Call Google Generative AI untuk analisis
   - Parse response JSON dari AI
4. **Archive Service Integration**: Simpan hasil ke Archive Service

**Request ke Archive Service:**
```
POST /archive/results
Headers:
  X-Internal-Service: true
  X-Service-Key: internal_service_secret_key
  Content-Type: application/json
```

**Body:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "assessment_data": {
    "riasec": { ... },
    "ocean": { ... },
    "viaIs": { ... },
    "multipleIntelligences": { ... },
    "cognitiveStyleIndex": { ... }
  },
  "persona_profile": [
    {
      "archetype": "The Innovator",
      "description": "...",
      "strengths": [...],
      "challenges": [...],
      "career_recommendations": [...]
    }
  ],
  "status": "completed"
}
```

### 5. Archive Service - Data Storage (Pertama)

1. **Service Authentication**: Verifikasi `X-Service-Key` untuk internal service
2. **Data Validation**: Validasi struktur data menggunakan Joi schema
3. **Database Insert**: Simpan ke table `archive.analysis_results`:

```sql
INSERT INTO archive.analysis_results (
    user_id, 
    assessment_data, 
    persona_profile, 
    status
) VALUES (
    '550e8400-e29b-41d4-a716-************',
    '{"riasec": {...}, "ocean": {...}, ...}',
    '[{"archetype": "The Innovator", ...}]',
    'completed'
);
```

### 6. Notification Service - WebSocket Notification

1. **Notification Trigger**: Analysis Worker memanggil Notification Service setelah berhasil simpan ke Archive

**Request ke Notification Service:**
```
POST /notifications/analysis-complete
Headers:
  X-Internal-Service: true
  X-Service-Key: internal_service_secret_key
  Content-Type: application/json
```

**Body:**
```json
{
  "userId": "550e8400-e29b-41d4-a716-************",
  "jobId": "550e8400-e29b-41d4-a716-************",
  "resultId": "550e8400-e29b-41d4-a716-************",
  "status": "completed",
  "data": {
    "archetype": "The Innovator",
    "processingTime": "3.2 minutes"
  }
}
```

2. **WebSocket Emission**: Notification Service mengirim event ke user yang terhubung:

```javascript
// Event yang dikirim ke client
socket.emit('analysis-complete', {
  "type": "analysis-complete",
  "jobId": "550e8400-e29b-41d4-a716-************",
  "resultId": "550e8400-e29b-41d4-a716-************",
  "status": "completed",
  "timestamp": "2024-01-01T00:05:00.000Z",
  "message": "Your personality analysis is ready!",
  "data": {
    "archetype": "The Innovator",
    "processingTime": "3.2 minutes"
  }
});
```

### 7. Client - Receive Notification & Fetch Results

1. **WebSocket Connection**: Client terhubung ke Notification Service dengan JWT auth:

```javascript
const socket = io('http://localhost:3005', {
  auth: { token: 'jwt_token_here' }
});

socket.on('analysis-complete', (data) => {
  console.log('Analysis completed:', data);
  // Update UI, show notification, fetch results
});
```

2. **Fetch Results**: Setelah menerima notification, client memanggil API untuk mengambil hasil:

**Request:**
```
GET /archive/results?page=1&limit=10&status=completed
Headers:
  Authorization: Bearer <JWT_TOKEN>
```

### 8. Archive Service - Data Retrieval (Kedua)

1. **API Gateway Routing**: Request diteruskan dari API Gateway ke Archive Service
2. **JWT Authentication**: Verifikasi JWT token dan ekstrak user information
3. **Database Query**: Query results berdasarkan user_id:

```sql
SELECT id, user_id, persona_profile, status, created_at, updated_at
FROM archive.analysis_results 
WHERE user_id = '550e8400-e29b-41d4-a716-************'
ORDER BY created_at DESC
LIMIT 10 OFFSET 0;
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "user_id": "550e8400-e29b-41d4-a716-************",
      "persona_profile": [
        {
          "archetype": "The Innovator",
          "description": "...",
          "strengths": [...],
          "challenges": [...],
          "career_recommendations": [...]
        }
      ],
      "status": "completed",
      "created_at": "2024-01-01T00:05:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1
  }
}
```

## 🔧 Technical Implementation Details

### Database Schema

**Table: `archive.analysis_results`**
```sql
CREATE TABLE archive.analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    assessment_data JSONB,
    persona_profile JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

**Status Values:**
- `pending`: Initial status (tidak digunakan dalam implementasi saat ini)
- `processing`: Sedang diproses oleh Analysis Worker
- `completed`: Analisis selesai dan berhasil
- `failed`: Analisis gagal

### RabbitMQ Configuration

**Exchange:** `atma_exchange` (type: direct)
**Queue:** `assessment_analysis`
**Routing Key:** `analysis.process`

### Service Ports

- **API Gateway**: 3000
- **Auth Service**: 3001
- **Archive Service**: 3002
- **Assessment Service**: 3003
- **Analysis Worker**: 3004
- **Notification Service**: 3005

### Authentication Flow

1. **User Authentication**: JWT token dari Auth Service
2. **API Gateway**: Verifikasi JWT dan forward user context
3. **Internal Services**: Service-to-service authentication menggunakan `X-Service-Key`

## 🔄 Archive Service - Dual Role

Archive Service memang bekerja 2 kali dalam flow ini:

### Pertama: Menerima Data dari Analysis Worker
- **Endpoint**: `POST /archive/results`
- **Authentication**: Internal service key
- **Function**: Menyimpan hasil analisis ke database
- **Status**: Set status menjadi `completed`

### Kedua: Melayani Request dari Client
- **Endpoint**: `GET /archive/results`
- **Authentication**: JWT token via API Gateway
- **Function**: Mengambil hasil analisis untuk ditampilkan ke user
- **Filter**: Hanya data milik user yang authenticated

## ✅ Verification Status

Berdasarkan analisis kode, implementasi sudah sesuai dengan flow logic yang dijelaskan:

- ✅ **API Gateway**: JWT verification dan routing sudah benar
- ✅ **Assessment Service**: Queue publishing dan job tracking sudah implementasi
- ✅ **Analysis Worker**: Queue consumption, AI processing, dan integration ke Archive & Notification sudah benar
- ✅ **Archive Service**: Dual role (write dari Analysis Worker, read dari Client) sudah implementasi
- ✅ **Notification Service**: WebSocket notification sudah implementasi
- ✅ **Database Schema**: Table structure dan indexing sudah sesuai
- ✅ **Authentication**: JWT untuk user, service key untuk internal communication sudah benar

Flow logic ecosystem ATMA Backend sudah terimplementasi dengan lengkap dan sesuai dengan desain yang diinginkan.
