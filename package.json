{"name": "atma-backend", "version": "1.0.0", "description": "AI-Driven Talent Mapping Assessment Backend - Microservices Architecture", "main": "index.js", "scripts": {"security:audit": "node security-audit.js", "security:fix": "npm run security:fix:all", "security:fix:all": "cd api-gateway && npm audit fix && cd ../auth-service && npm audit fix && cd ../archive-service && npm audit fix && cd ../assessment-service && npm audit fix && cd ../analysis-worker && npm audit fix && cd ../notification-service && npm audit fix", "install:all": "npm run install:api-gateway && npm run install:auth && npm run install:archive && npm run install:assessment && npm run install:analysis && npm run install:notification", "install:api-gateway": "cd api-gateway && npm install", "install:auth": "cd auth-service && npm install", "install:archive": "cd archive-service && npm install", "install:assessment": "cd assessment-service && npm install", "install:analysis": "cd analysis-worker && npm install", "install:notification": "cd notification-service && npm install", "start:all": "concurrently \"npm run start:api-gateway\" \"npm run start:auth\" \"npm run start:archive\" \"npm run start:assessment\" \"npm run start:analysis\" \"npm run start:notification\"", "start:api-gateway": "cd api-gateway && npm start", "start:auth": "cd auth-service && npm start", "start:archive": "cd archive-service && npm start", "start:assessment": "cd assessment-service && npm start", "start:analysis": "cd analysis-worker && npm start", "start:notification": "cd notification-service && npm start", "dev:all": "concurrently \"npm run dev:api-gateway\" \"npm run dev:auth\" \"npm run dev:archive\" \"npm run dev:assessment\" \"npm run dev:analysis\" \"npm run dev:notification\"", "dev:api-gateway": "cd api-gateway && npm run dev", "dev:auth": "cd auth-service && npm run dev", "dev:archive": "cd archive-service && npm run dev", "dev:assessment": "cd assessment-service && npm run dev", "dev:analysis": "cd analysis-worker && npm run dev", "dev:notification": "cd notification-service && npm run dev", "test:all": "npm run test:api-gateway && npm run test:auth && npm run test:archive && npm run test:assessment && npm run test:analysis && npm run test:notification", "test:api-gateway": "cd api-gateway && npm test", "test:auth": "cd auth-service && npm test", "test:archive": "cd archive-service && npm test", "test:assessment": "cd assessment-service && npm test", "test:analysis": "cd analysis-worker && npm test", "test:notification": "cd notification-service && npm test", "test:e2e": "node scripts/test-end-to-end.js", "test:e2e:install": "npm install axios pg socket.io-client"}, "keywords": ["microservices", "assessment", "ai", "talent-mapping", "nodejs", "express", "postgresql", "rabbitmq"], "author": "ATMA Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0", "axios": "^1.6.0", "pg": "^8.11.0", "socket.io-client": "^4.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}