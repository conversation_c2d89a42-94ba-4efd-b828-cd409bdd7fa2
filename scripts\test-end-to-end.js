/**
 * End-to-End Testing Script
 * Tests complete flow: Client → API Gateway → Assessment Service → Analysis Worker → Archive Service
 * 
 * Prerequisites:
 * - All services running (API Gateway, Auth, Assessment, Analysis Worker, Archive, Notification)
 * - PostgreSQL database running
 * - RabbitMQ running
 * 
 * Usage: node scripts/test-end-to-end.js
 */

const axios = require('axios');
const { Client } = require('pg');
const io = require('socket.io-client');

// Configuration
const config = {
  apiGateway: 'http://localhost:3000',
  notificationService: 'http://localhost:3005',
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'atma_db',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password'
  },
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword123'
  },
  timeout: 120000 // 2 minutes timeout for analysis
};

// Test data
const assessmentData = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    openness: 80,
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    love_of_learning: 82,
    perspective: 75,
    bravery: 68,
    perseverance: 80,
    honesty: 85,
    zest: 70,
    love: 65,
    kindness: 78,
    social_intelligence: 72,
    teamwork: 75,
    fairness: 80,
    leadership: 68,
    forgiveness: 70,
    humility: 65,
    prudence: 75,
    self_regulation: 78,
    appreciation_of_beauty: 82,
    gratitude: 85,
    hope: 80,
    humor: 70,
    spirituality: 60
  },
  multipleIntelligences: {
    linguistic: 85,
    logicalMathematical: 90,
    spatial: 75,
    bodilyKinesthetic: 60,
    musical: 70,
    interpersonal: 65,
    intrapersonal: 80,
    naturalistic: 55
  },
  cognitiveStyleIndex: {
    analytic: 80,
    intuitive: 60
  }
};

// Global variables
let dbClient;
let jwtToken;
let userId;
let jobId;
let resultId;
let socket;

/**
 * Initialize database connection
 */
async function initDatabase() {
  console.log('🔌 Connecting to database...');
  dbClient = new Client(config.database);
  await dbClient.connect();
  console.log('✅ Database connected');
}

/**
 * Cleanup database connection
 */
async function cleanupDatabase() {
  if (dbClient) {
    await dbClient.end();
    console.log('🔌 Database connection closed');
  }
}

/**
 * Create test user and get JWT token
 */
async function setupTestUser() {
  console.log('\n📝 Setting up test user...');
  
  try {
    // Try to register user (might fail if already exists)
    await axios.post(`${config.apiGateway}/auth/register`, {
      email: config.testUser.email,
      password: config.testUser.password
    });
    console.log('✅ Test user registered');
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('ℹ️  Test user already exists');
    } else {
      console.log('⚠️  Registration failed:', error.response?.data?.message || error.message);
    }
  }

  // Login to get JWT token
  try {
    const loginResponse = await axios.post(`${config.apiGateway}/auth/login`, {
      email: config.testUser.email,
      password: config.testUser.password
    });

    jwtToken = loginResponse.data.data.token;
    userId = loginResponse.data.data.user.id;
    
    console.log('✅ Test user logged in');
    console.log(`   User ID: ${userId}`);
    console.log(`   Token: ${jwtToken.substring(0, 20)}...`);
    
    return true;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Setup WebSocket connection for notifications
 */
async function setupWebSocket() {
  return new Promise((resolve, reject) => {
    console.log('\n🔌 Setting up WebSocket connection...');
    
    socket = io(config.notificationService, {
      auth: { token: jwtToken },
      timeout: 10000
    });

    socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      resolve();
    });

    socket.on('connect_error', (error) => {
      console.error('❌ WebSocket connection failed:', error.message);
      reject(error);
    });

    socket.on('analysis-complete', (data) => {
      console.log('🔔 Received analysis completion notification:', {
        jobId: data.jobId,
        resultId: data.resultId,
        status: data.status,
        archetype: data.data?.archetype
      });
      resultId = data.resultId;
    });

    socket.on('disconnect', () => {
      console.log('🔌 WebSocket disconnected');
    });
  });
}

/**
 * Submit assessment data
 */
async function submitAssessment() {
  console.log('\n📊 Submitting assessment data...');
  
  try {
    const response = await axios.post(
      `${config.apiGateway}/assessments/submit`,
      assessmentData,
      {
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    jobId = response.data.data.jobId;
    
    console.log('✅ Assessment submitted successfully');
    console.log(`   Job ID: ${jobId}`);
    console.log(`   Status: ${response.data.data.status}`);
    console.log(`   Estimated time: ${response.data.data.estimatedProcessingTime}`);
    
    return true;
  } catch (error) {
    console.error('❌ Assessment submission failed:', error.response?.data?.message || error.message);
    if (error.response?.data?.errors) {
      console.error('   Validation errors:', error.response.data.errors);
    }
    return false;
  }
}

/**
 * Wait for analysis completion
 */
async function waitForAnalysisCompletion() {
  console.log('\n⏳ Waiting for analysis completion...');
  
  const startTime = Date.now();
  const maxWaitTime = config.timeout;
  
  return new Promise((resolve, reject) => {
    const checkInterval = setInterval(async () => {
      const elapsedTime = Date.now() - startTime;
      
      if (elapsedTime > maxWaitTime) {
        clearInterval(checkInterval);
        reject(new Error('Analysis timeout - exceeded maximum wait time'));
        return;
      }

      // Check if we received notification
      if (resultId) {
        clearInterval(checkInterval);
        console.log(`✅ Analysis completed in ${Math.round(elapsedTime / 1000)} seconds`);
        resolve();
        return;
      }

      // Check job status via API
      try {
        const statusResponse = await axios.get(
          `${config.apiGateway}/assessments/status/${jobId}`,
          {
            headers: { 'Authorization': `Bearer ${jwtToken}` }
          }
        );

        const status = statusResponse.data.data.status;
        console.log(`   Status: ${status} (${Math.round(elapsedTime / 1000)}s elapsed)`);

        if (status === 'completed') {
          clearInterval(checkInterval);
          console.log(`✅ Analysis completed in ${Math.round(elapsedTime / 1000)} seconds`);
          resolve();
        } else if (status === 'failed') {
          clearInterval(checkInterval);
          reject(new Error('Analysis failed'));
        }
      } catch (error) {
        console.log(`   Status check failed: ${error.message}`);
      }
    }, 5000); // Check every 5 seconds
  });
}

/**
 * Fetch results from Archive Service
 */
async function fetchResults() {
  console.log('\n📥 Fetching analysis results...');
  
  try {
    const response = await axios.get(
      `${config.apiGateway}/archive/results?limit=1&status=completed`,
      {
        headers: { 'Authorization': `Bearer ${jwtToken}` }
      }
    );

    const results = response.data.data;
    
    if (results.length === 0) {
      console.log('❌ No results found');
      return null;
    }

    const latestResult = results[0];
    console.log('✅ Results fetched successfully');
    console.log(`   Result ID: ${latestResult.id}`);
    console.log(`   Status: ${latestResult.status}`);
    console.log(`   Created: ${latestResult.created_at}`);
    
    if (latestResult.persona_profile && latestResult.persona_profile.length > 0) {
      const profile = latestResult.persona_profile[0];
      console.log(`   Archetype: ${profile.archetype || 'N/A'}`);
      console.log(`   Strengths: ${profile.strengths?.length || 0} items`);
      console.log(`   Challenges: ${profile.challenges?.length || 0} items`);
      console.log(`   Career Recommendations: ${profile.career_recommendations?.length || 0} items`);
    }

    return latestResult;
  } catch (error) {
    console.error('❌ Failed to fetch results:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * Verify data in database directly
 */
async function verifyDatabaseData() {
  console.log('\n🔍 Verifying data in database...');
  
  try {
    // Check if result exists in database
    const query = `
      SELECT 
        id, 
        user_id, 
        status, 
        assessment_data,
        persona_profile,
        created_at,
        updated_at
      FROM archive.analysis_results 
      WHERE user_id = $1 
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    const result = await dbClient.query(query, [userId]);
    
    if (result.rows.length === 0) {
      console.log('❌ No data found in database');
      return false;
    }

    const dbRecord = result.rows[0];
    console.log('✅ Data verified in database');
    console.log(`   Record ID: ${dbRecord.id}`);
    console.log(`   User ID: ${dbRecord.user_id}`);
    console.log(`   Status: ${dbRecord.status}`);
    console.log(`   Created: ${dbRecord.created_at}`);
    
    // Verify assessment data
    if (dbRecord.assessment_data) {
      const assessmentKeys = Object.keys(dbRecord.assessment_data);
      console.log(`   Assessment data keys: ${assessmentKeys.join(', ')}`);
      
      // Check if all expected assessment types are present
      const expectedKeys = ['riasec', 'ocean', 'viaIs', 'multipleIntelligences', 'cognitiveStyleIndex'];
      const missingKeys = expectedKeys.filter(key => !assessmentKeys.includes(key));
      
      if (missingKeys.length === 0) {
        console.log('   ✅ All assessment data types present');
      } else {
        console.log(`   ⚠️  Missing assessment data: ${missingKeys.join(', ')}`);
      }
    }
    
    // Verify persona profile
    if (dbRecord.persona_profile && Array.isArray(dbRecord.persona_profile)) {
      const profile = dbRecord.persona_profile[0];
      if (profile) {
        console.log(`   Persona archetype: ${profile.archetype || 'N/A'}`);
        console.log(`   Profile sections: ${Object.keys(profile).join(', ')}`);
      }
      console.log('   ✅ Persona profile data present');
    } else {
      console.log('   ❌ Persona profile data missing or invalid');
    }

    return true;
  } catch (error) {
    console.error('❌ Database verification failed:', error.message);
    return false;
  }
}

/**
 * Cleanup test data
 */
async function cleanupTestData() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    // Delete test results
    await dbClient.query(
      'DELETE FROM archive.analysis_results WHERE user_id = $1',
      [userId]
    );
    
    // Delete test user
    await dbClient.query(
      'DELETE FROM auth.users WHERE email = $1',
      [config.testUser.email]
    );
    
    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
  }
}

/**
 * Main test function
 */
async function runEndToEndTest() {
  console.log('🚀 Starting End-to-End Test');
  console.log('=' .repeat(60));
  
  let success = false;
  
  try {
    // Initialize
    await initDatabase();
    
    // Setup test user
    const userSetup = await setupTestUser();
    if (!userSetup) {
      throw new Error('Failed to setup test user');
    }
    
    // Setup WebSocket
    await setupWebSocket();
    
    // Submit assessment
    const submitted = await submitAssessment();
    if (!submitted) {
      throw new Error('Failed to submit assessment');
    }
    
    // Wait for completion
    await waitForAnalysisCompletion();
    
    // Fetch results via API
    const apiResults = await fetchResults();
    if (!apiResults) {
      throw new Error('Failed to fetch results via API');
    }
    
    // Verify in database
    const dbVerified = await verifyDatabaseData();
    if (!dbVerified) {
      throw new Error('Failed to verify data in database');
    }
    
    success = true;
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  } finally {
    // Cleanup
    if (socket) {
      socket.disconnect();
    }
    
    // Only cleanup if test was successful (optional)
    // await cleanupTestData();
    
    await cleanupDatabase();
  }
  
  console.log('\n' + '='.repeat(60));
  if (success) {
    console.log('🎉 End-to-End Test PASSED');
    console.log('\nFlow verified:');
    console.log('  ✅ Client → API Gateway → Assessment Service');
    console.log('  ✅ Assessment Service → RabbitMQ → Analysis Worker');
    console.log('  ✅ Analysis Worker → Archive Service (save)');
    console.log('  ✅ Analysis Worker → Notification Service');
    console.log('  ✅ Client ← WebSocket ← Notification Service');
    console.log('  ✅ Client → API Gateway → Archive Service (fetch)');
    console.log('  ✅ Database verification');
  } else {
    console.log('❌ End-to-End Test FAILED');
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Test interrupted');
  if (socket) socket.disconnect();
  await cleanupDatabase();
  process.exit(0);
});

// Run the test
if (require.main === module) {
  runEndToEndTest().catch(console.error);
}

module.exports = { runEndToEndTest };
